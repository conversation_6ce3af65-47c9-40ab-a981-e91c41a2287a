2025-07-24 16:18:59,294 - __main__ - INFO - Starting battery timeline analysis...
2025-07-24 16:18:59,294 - __main__ - INFO - Loading data files...
2025-07-24 16:18:59,294 - __main__ - INFO - Loading battery lifecycle timelines...
2025-07-24 16:18:59,365 - __main__ - INFO - Loaded 29221 timeline records
2025-07-24 16:18:59,365 - __main__ - INFO - Loading battery type data...
2025-07-24 16:18:59,384 - __main__ - INFO - Loaded 18027 battery type records
2025-07-24 16:18:59,384 - __main__ - INFO - Cleaning data...
2025-07-24 16:18:59,419 - __main__ - INFO - Total removed: 2 rows (missing battery_id + invalid intervals)
2025-07-24 16:18:59,432 - __main__ - INFO - After cleaning: 29219 timeline records, 18026 battery type records
2025-07-24 16:18:59,432 - __main__ - INFO - Processing data for accure.csv output...
2025-07-24 16:18:59,465 - __main__ - INFO - Pre-indexing battery timelines for fast lookup...
2025-07-24 16:19:02,708 - __main__ - INFO - Processing 18017 unique batteries
2025-07-24 16:19:29,549 - __main__ - INFO - Processed 18017 battery records
2025-07-24 16:19:29,549 - __main__ - INFO - Batteries with type: 18017
2025-07-24 16:19:29,549 - __main__ - INFO - Batteries without type: 0
2025-07-24 16:19:29,550 - __main__ - INFO - Validating result...
2025-07-24 16:19:29,569 - __main__ - INFO - Generating accure.csv output...
2025-07-24 16:19:29,672 - __main__ - INFO - Saved 18017 records to accure.csv
2025-07-24 16:19:29,672 - __main__ - INFO - Saved statistics to analyze_battery_timeline_statistics.txt
2025-07-24 16:19:29,673 - __main__ - INFO - Analysis completed successfully!
2025-07-24 16:19:29,673 - __main__ - INFO - Output files: accure.csv, analyze_battery_timeline_statistics.txt
2025-07-24 16:30:04,819 - __main__ - INFO - Starting battery timeline analysis...
2025-07-24 16:30:04,819 - __main__ - INFO - Loading data files...
2025-07-24 16:30:04,819 - __main__ - INFO - Loading battery lifecycle timelines...
2025-07-24 16:30:04,882 - __main__ - INFO - Loaded 29221 timeline records
2025-07-24 16:30:04,882 - __main__ - INFO - Loading battery type data...
2025-07-24 16:30:04,894 - __main__ - INFO - Loaded 18027 battery type records
2025-07-24 16:30:04,894 - __main__ - INFO - Cleaning data...
2025-07-24 16:30:04,926 - __main__ - INFO - Total removed: 2 rows (missing battery_id + invalid intervals)
2025-07-24 16:30:04,937 - __main__ - INFO - After cleaning: 29219 timeline records, 18026 battery type records
2025-07-24 16:30:04,937 - __main__ - INFO - Processing data for accure.csv output...
2025-07-24 16:30:04,968 - __main__ - INFO - Pre-indexing battery timelines for fast lookup...
2025-07-24 16:30:08,304 - __main__ - INFO - Processing 18017 unique batteries
2025-07-24 16:30:38,155 - __main__ - INFO - Processed 18017 battery records
2025-07-24 16:30:38,155 - __main__ - INFO - Batteries with type: 18017
2025-07-24 16:30:38,155 - __main__ - INFO - Batteries without type: 0
2025-07-24 16:30:38,157 - __main__ - INFO - Validating result...
2025-07-24 16:30:38,179 - __main__ - INFO - Generating accure.csv output...
2025-07-24 16:30:38,293 - __main__ - INFO - Saved 18017 records to accure.csv
2025-07-24 16:30:38,293 - __main__ - INFO - Saved statistics to analyze_battery_timeline_statistics.txt
2025-07-24 16:30:38,294 - __main__ - INFO - Analysis completed successfully!
2025-07-24 16:30:38,294 - __main__ - INFO - Output files: accure.csv, analyze_battery_timeline_statistics.txt
